import os
import glob
import numpy as np
import cv2

# === baseline 3D box estimator ===
KITTI_MEAN_DIMS = {
    "Car": [1.5, 1.6, 3.9],   # h, w, l
    "Pedestrian": [1.7, 0.6, 0.8],
    "Cyclist": [1.7, 0.6, 1.7],
}

def get_3d_box(box2d_query, intrinsic, obj_class="Car"):
    """Baseline geometric 3D bounding box estimation."""
    fx, fy, cx, cy = intrinsic
    min_x, min_y, max_x, max_y = box2d_query

    # 1. 2D box height and center
    h_2d = max_y - min_y
    u = (min_x + max_x) / 2.0
    v = (min_y + max_y) / 2.0

    # 2. Depth from geometry
    H_3d, W_3d, L_3d = KITTI_MEAN_DIMS.get(obj_class, KITTI_MEAN_DIMS["Car"])
    Z = (fy * H_3d) / (h_2d + 1e-6)

    # 3. Back-project 2D center
    X = (u - cx) * Z / fx
    Y = (v - cy) * Z / fy

    alpha = 0.0
    sx, sy, sz = W_3d, H_3d, L_3d
    return [X, Y, Z, sx, sy, sz, alpha]

def load_calib(calib_path):
    """Load KITTI calibration (P2 matrix)."""
    with open(calib_path, "r") as f:
        for line in f:
            if line.startswith("P2:"):
                vals = line.strip().split(" ")[1:]
                P2 = np.array([float(x) for x in vals], dtype=np.float32).reshape(3, 4)
                fx, fy, cx, cy = P2[0,0], P2[1,1], P2[0,2], P2[1,2]
                return [fx, fy, cx, cy]
    raise ValueError("P2 matrix not found in calibration file.")

def load_labels(label_path):
    """Load 2D boxes and object types."""
    labels = []
    with open(label_path, "r") as f:
        for line in f:
            parts = line.strip().split(" ")
            obj_type = parts[0]
            if obj_type == "DontCare":
                continue
            bbox_2d = [float(x) for x in parts[4:8]]
            labels.append((obj_type, bbox_2d))
    return labels

if __name__ == "__main__":
    image_dir = "./kitti_tiny_3D/training/image_2"
    calib_dir = "./kitti_tiny_3D/training/calib"
    label_dir = "./kitti_tiny_3D/training/label_2"

    image_files = sorted(glob.glob(os.path.join(image_dir, "*.png")))

    with open("predicted.txt", "w") as f_out:
        for image_path in image_files:
            file_id = os.path.splitext(os.path.basename(image_path))[0]
            calib_path = os.path.join(calib_dir, file_id + ".txt")
            label_path = os.path.join(label_dir, file_id + ".txt")

            intrinsics = load_calib(calib_path)
            labels = load_labels(label_path)

            for obj_type, bbox_2d in labels:
                pred_box = get_3d_box(bbox_2d, intrinsics, obj_class=obj_type)
                line = [file_id] + pred_box + [obj_type]
                f_out.write(" ".join(map(str, line)) + "\n")

    print("Predictions saved to predicted.txt")
