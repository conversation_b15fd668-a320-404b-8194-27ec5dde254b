import cv2
import numpy as np
import os


def load_calib(calib_path):
    """Load KITTI calibration (P2 projection matrix)."""
    with open(calib_path, "r") as f:
        for line in f:
            if line.startswith("P2:"):
                vals = line.strip().split(" ")[1:]
                P2 = np.array([float(x) for x in vals], dtype=np.float32).reshape(3, 4)
                return P2
    raise ValueError("P2 matrix not found in calibration file.")


def load_labels(label_path):
    """Load KITTI labels for a single image."""
    labels = []
    with open(label_path, "r") as f:
        for line in f:
            parts = line.strip().split(" ")
            obj_type = parts[0]
            # skip DontCare regions
            if obj_type == "DontCare":
                continue

            bbox_2d = [float(x) for x in parts[4:8]]
            dims = [float(x) for x in parts[8:11]]  # height, width, length
            loc = [float(x) for x in parts[11:14]]  # x, y, z in camera coords
            ry = float(parts[14])  # rotation around Y axis
            labels.append((obj_type, bbox_2d, dims, loc, ry))
    return labels


def compute_box_3d(dim, location, ry):
    """Compute 3D bounding box corners in camera coordinates."""
    h, w, l = dim
    # 3D bounding box corners relative to object center
    x_corners = [l/2, l/2, -l/2, -l/2, l/2, l/2, -l/2, -l/2]
    y_corners = [0, 0, 0, 0, -h, -h, -h, -h]
    z_corners = [w/2, -w/2, -w/2, w/2, w/2, -w/2, -w/2, w/2]

    corners = np.array([x_corners, y_corners, z_corners])

    # Rotation matrix around yaw (Y-axis)
    R = np.array([
        [np.cos(ry), 0, np.sin(ry)],
        [0, 1, 0],
        [-np.sin(ry), 0, np.cos(ry)]
    ])
    corners_rot = R @ corners
    corners_rot += np.array(location).reshape(3, 1)
    return corners_rot


def project_to_image(pts_3d, P):
    """Project 3D points into the image plane."""
    pts_3d_homo = np.vstack((pts_3d, np.ones((1, pts_3d.shape[1]))))
    pts_2d = P @ pts_3d_homo
    pts_2d[:2] /= pts_2d[2]
    return pts_2d[:2].T


def draw_2d_box(img, bbox):
    """Draw 2D bounding box."""
    x1, y1, x2, y2 = [int(v) for v in bbox]
    return cv2.rectangle(img, (x1, y1), (x2, y2), (0, 255, 0), 2)


def draw_3d_box(img, corners_2d):
    """Draw projected 3D bounding box."""
    corners_2d = corners_2d.astype(int)
    # 4 bottom corners
    for k in range(4):
        i, j = k, (k+1) % 4
        cv2.line(img, tuple(corners_2d[i]), tuple(corners_2d[j]), (255, 0, 0), 2)
        cv2.line(img, tuple(corners_2d[i+4]), tuple(corners_2d[j+4]), (255, 0, 0), 2)
        cv2.line(img, tuple(corners_2d[i]), tuple(corners_2d[i+4]), (255, 0, 0), 2)
    return img


def visualize_sample(image_path, calib_path, label_path, save_path=None):
    """Visualize image with 2D and 3D bounding boxes."""
    img = cv2.imread(image_path)
    P2 = load_calib(calib_path)
    labels = load_labels(label_path)

    for obj_type, bbox_2d, dims, loc, ry in labels:
        img = draw_2d_box(img, bbox_2d)

        corners_3d = compute_box_3d(dims, loc, ry)
        corners_2d = project_to_image(corners_3d, P2)
        img = draw_3d_box(img, corners_2d)

    if save_path:
        cv2.imwrite(save_path, img)
    else:
        cv2.imshow("Visualization", img)
        cv2.waitKey(0)
        cv2.destroyAllWindows()


if __name__ == "__main__":
    import glob

    image_dir = "./kitti_tiny_3D/training/image_2"
    calib_dir = "./kitti_tiny_3D/training/calib"
    label_dir = "./kitti_tiny_3D/training/label_2"

    image_files = sorted(glob.glob(os.path.join(image_dir, "*.png")))
    idx = 0

    while True:
        image_path = image_files[idx]
        file_id = os.path.splitext(os.path.basename(image_path))[0]
        calib_path = os.path.join(calib_dir, file_id + ".txt")
        label_path = os.path.join(label_dir, file_id + ".txt")

        img = cv2.imread(image_path)
        P2 = load_calib(calib_path)
        labels = load_labels(label_path)

        for obj_type, bbox_2d, dims, loc, ry in labels:
            img = draw_2d_box(img, bbox_2d)
            # corners_3d = compute_box_3d(dims, loc, ry)
            # corners_2d = project_to_image(corners_3d, P2)
            # img = draw_3d_box(img, corners_2d)

        cv2.imshow("Visualization", img)
        key = cv2.waitKey(0) & 0xFF

        if key == ord("d"):  # forward
            idx = (idx + 1) % len(image_files)
        elif key == ord("a"):  # backward
            idx = (idx - 1) % len(image_files)
        elif key == ord("q"):  # quit
            break

    cv2.destroyAllWindows()
