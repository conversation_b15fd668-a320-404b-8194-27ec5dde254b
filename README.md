# 3D Bounding Box Estimation

## Core Task
The goal of this project is to **estimate an object's 3D bounding box** (location, dimensions, orientation) in the camera's coordinate system, given:

- A single **RGB image**
- The **camera's intrinsic parameters** (fx, fy, cx, cy)
- A **2D bounding box query** (min_x, min_y, max_x, max_y)

The output is a **3D bounding box** defined by 7 parameters:
- `(cx, cy, cz)` → bottom center of the box in camera coordinates
- `(sx, sy, sz)` → box dimensions (width, height, length)
- `alpha` → yaw rotation angle (orientation around the vertical axis)

---

## Steps to Approach

1. **Data Visualization**  
   - Implement a script to visualize 2D bounding boxes and project 3D bounding boxes onto the image for debugging.

2. **Baseline Estimation**  
   - Use geometric constraints (e.g., object height vs. pixel height with camera intrinsics) to get a first estimate of depth and size.  
   - Explore simple methods inspired by *Deep3DBox*.

3. **Model Design**  
   - Option A: Geometry-based regression of 3D box parameters.  
   - Option B: CNN-based regression to directly predict 3D location, dimensions, and orientation.  

4. **Training & Evaluation**  
   - Train on KITTI dataset samples.  
   - Evaluate using 3D Intersection-over-Union (IoU) and separate errors for position, size, and orientation.  

5. **Inference Mode**  
   - Given an input image, intrinsics, and 2D box, predict the 3D bounding box.  
   - Save the results with visualizations of both 2D query box and projected 3D bounding box.  

---

## Environment Setup (Mac + Conda)

For visualization and debugging, you only need **numpy** and **opencv**.  

```bash
# Create and activate conda environment
conda create -n kitti_vis python=3.9 -y
conda activate kitti_vis

# Install required libraries
conda install numpy -y
conda install -c conda-forge opencv -y
